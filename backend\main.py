#!/usr/bin/env python3
"""
FastAPI main application file for the Vanna PostgreSQL Ollama ChromaDB application.
Converted from Flask to FastAPI.
"""

import logging
import sys
import os
import time
import traceback
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from config import Config
from app.utils import MemoryCache
from app.core import setup_vanna
from app.core.app_database import get_app_database_service, close_app_database_service
from app.api.main_routes import router as main_router
from app.api.sql_routes import router as sql_router
from app.api.analysis_routes import router as analysis_router
from app.api.dashboard_routes import router as dashboard_router
from app.api.search_routes import router as search_router
from app.api.chat_routes import router as chat_router
from app.auth.routes import router as auth_router
from app.middleware import APIVersionMiddleware

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# Global variables
vn = None
cache = None
config = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events"""
    global vn, cache, config
    
    logger.info("=== APPLICATION STARTUP INITIATED ===")
    logger.info(f"Startup time: {datetime.now().isoformat()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    try:
        # Load configuration
        logger.info("Loading application configuration...")
        config = Config()
        logger.info(f"Configuration loaded successfully: {config.__class__.__name__}")
        
        # Initialize cache with persistence
        logger.info("Initializing cache with persistence...")
        cache_dir = os.path.join(os.getcwd(), config.CACHE_DIR)
        cache = MemoryCache(
            max_size=config.CACHE_MAX_SIZE, 
            expiration_seconds=config.CACHE_EXPIRATION_SECONDS,
            persistent_dir=cache_dir
        )
        # Run cleanup of old chat history files (older than 30 days)
        cache.cleanup_old_files(max_age_days=30)
        
        # Clean up corrupted cache files
        corrupted_count = cache.clean_corrupted_files()
        if corrupted_count > 0:
            logger.warning(f"Found and backed up {corrupted_count} corrupted chat history files")
            
        logger.info(f"Cache initialized successfully with persistence at {cache_dir}!")
        
        # Initialize Vanna
        logger.info("Initializing Vanna...")
        vn = setup_vanna()
        logger.info("Vanna initialized successfully!")

        # Initialize App Database Service
        logger.info("Initializing App Database Service...")
        app_db_service = get_app_database_service()
        logger.info("App Database Service initialized successfully!")

        # Store instances in app state
        app.state.vn = vn
        app.state.cache = cache
        app.state.config = config
        app.state.app_db_service = app_db_service
        
        # Log startup information
        logger.info("=== APPLICATION CONFIGURATION ===")
        print("🚀 Starting Vanna FastAPI Application...")
        print(f"🌐 Access the application at: http://localhost:8000")
        print(f"🔧 Debug mode: {config.DEBUG}")
        print(f"📊 Database: {config.DB_HOST}:{config.DB_PORT}/{config.DB_NAME}")
        print(f"🤖 Ollama model: {config.OLLAMA_MODEL}")
        print("=" * 60)
        print("🔍 MODEL INPUT/OUTPUT LOGGING ENABLED")
        print("📋 You will see detailed logs in the terminal showing:")
        print("   • Full prompts sent to the model")
        print("   • Context data (DDL, docs, examples)")
        print("   • Real-time model responses")
        print("   • Summary and chart generation requests")
        print("   • SQL correction attempts")
        print("=" * 60)
        
        logger.info("=== FASTAPI SERVER READY ===")
        
    except Exception as e:
        logger.error(f"Application startup failed: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.exception("Full traceback:")
        raise
    
    yield  # Application is running

    # Shutdown
    logger.info("=== APPLICATION SHUTDOWN ===")
    logger.info(f"Shutdown time: {datetime.now().isoformat()}")

    # Close app database service
    try:
        close_app_database_service()
        logger.info("App Database Service closed successfully")
    except Exception as e:
        logger.error(f"Error closing App Database Service: {e}")

# Create FastAPI app
app = FastAPI(
    title="Hospital Assistant System",
    description="""
    AI-powered hospital management system with natural language SQL queries

    ## API Endpoints - V1 Only

    All API endpoints use the `/api/v1/` prefix:

    - **Health**: `/api/v1/health`
    - **Authentication**: `/api/v1/auth/` (login, logout, me)
    - **Chat Sessions**: `/api/v1/chat/` (create, list, update, delete sessions and messages)
    - **SQL Generation**: `/api/v1/sql/` (generate_sql, run_sql)
    - **Analysis**: `/api/v1/analysis/` (summaries, charts)
    - **Dashboard**: `/api/v1/dashboard/` (statistics, metrics)
    - **Search**: `/api/v1/search/` (patients, consultations, doctors)

    ## Authentication
    Most endpoints require Bearer token authentication.
    Use `/api/v1/auth/login` to obtain an access token.
    """,
    version="2.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])

# Add API versioning middleware
app.add_middleware(APIVersionMiddleware, api_version="2.0.0")

# Request/Response middleware for performance monitoring
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    logger.info(f"Request to {request.url.path} took {process_time:.2f}s")
    return response

# HTTPException handler to ensure proper JSON format
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"HTTPException: {exc.status_code} - {exc.detail}")

    # If detail is already a dict, return it as-is
    if isinstance(exc.detail, dict):
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.detail
        )

    # If detail is a string, wrap it in our standard format
    return JSONResponse(
        status_code=exc.status_code,
        content={"type": "error", "error": str(exc.detail)}
    )

# Global exception handler - only catch non-HTTP exceptions
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # Don't catch HTTPExceptions - they're handled above
    from fastapi import HTTPException
    if isinstance(exc, HTTPException):
        raise exc

    logger.error(f"Unhandled exception: {str(exc)}\n{traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={"type": "error", "error": "An unexpected error occurred. Please try again later."}
    )

# Include API routers

# ============================================================================
# API V1 ROUTES - SINGLE VERSION STRATEGY
# ============================================================================
# All endpoints use /api/v1/ prefix for consistency and future-proofing
# ============================================================================

# V1 API Routes (All endpoints properly versioned)
app.include_router(main_router, prefix="/api/v1", tags=["health"])
app.include_router(auth_router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(sql_router, prefix="/api/v1/sql", tags=["sql"])
app.include_router(analysis_router, prefix="/api/v1/analysis", tags=["analysis"])
app.include_router(dashboard_router, prefix="/api/v1", tags=["dashboard"])  # Dashboard routes already include /dashboard
app.include_router(search_router, prefix="/api/v1", tags=["search"])  # Search routes already include /search
app.include_router(chat_router, prefix="/api/v1/chat", tags=["chat"])  # Chat session management

# Static files mounting disabled - using React frontend instead
# app.mount("/", StaticFiles(directory="static", html=True), name="static")

# Utility functions to get global instances
def get_vanna():
    """Get the global Vanna instance"""
    return vn

def get_cache():
    """Get the global cache instance"""
    return cache

def get_config():
    """Get the global config instance"""
    return config

if __name__ == "__main__":
    import uvicorn
    
    # Load config for uvicorn settings
    config = Config()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=config.DEBUG,
        log_level="info"
    )
