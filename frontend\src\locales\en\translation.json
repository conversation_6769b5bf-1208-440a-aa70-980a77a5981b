{"actions.tryAgain": "Try again", "buttons.close": "Close", "buttons.closeModal": "Close modal", "buttons.tryAgain": "Try again", "chart.download": "Download", "chart.downloadAsPng": "Download as PNG", "chart.errorRendering": "Error rendering chart", "chart.interactiveInfo": "Interactive chart - hover for details", "chart.noData": "No chart data available", "chart.resize": "Resize chart", "chart.tryRefreshing": "Please try refreshing the page", "chat.input.loading": "Loading", "chat.input.placeholder": "Type your message...", "chat.input.send": "Send Message", "chat.input.sendIcon": "Send", "chat.sessions.defaultTitle_one": "", "chat.sessions.defaultTitle_other": "", "chatbot.error.chartNotGenerated": "Chart could not be generated at this time.", "chatbot.error.connectionLost": "Error: Connection lost. Please try again.", "chatbot.error.couldNotGenerateChart": "Could not generate a chart for this data.", "chatbot.error.couldNotGenerateSummary": "Could not generate summary.", "chatbot.error.errorGeneratingSummary": "Error generating summary.", "chatbot.error.failedToGenerateSummary": "Failed to generate summary.", "chatbot.error.failedToSend": "Error: Failed to send message. Please try again.", "chatbot.error.fetchingChart": "Error fetching chart.", "chatbot.error.sqlCorrectionBothFailed": "❌ Original SQL failed - Attempted automatic correction also failed", "chatbot.error.sqlCorrectionBothFailedDetails": "Error Details:\n- Initial SQL Error: {{error}}\n- Correction attempt also failed", "chatbot.error.sqlCorrectionInvalid": "❌ Original SQL failed - Automatic correction was not valid", "chatbot.error.sqlCorrectionInvalidDetails": "Error Details:\n- Initial SQL Error: {{error}}\n- LLM provided invalid correction: {{correction}}", "chatbot.error.sqlExecutionFailed": "SQL Execution Failed: {{error}}", "chatbot.error.sqlExecutionFailedGeneric": "The SQL query could not be executed. Please try rephrasing your question.", "chatbot.error.sqlExecutionFailedWithReason": "SQL Execution Failed: {{reason}}", "chatbot.error.sqlQueryOrResult": "An error occurred while running the SQL query or processing its results.", "chatbot.header.clearChat": "Clear Chat", "chatbot.header.clearChatTooltip": "Clear current chat", "chatbot.header.history": "History", "chatbot.header.title": "Hospital Data Assistant", "chatbot.header.toggleHistoryTooltip": "Toggle chat history", "chatbot.input.placeholder": "Type your question about hospital data...", "chatbot.loadingChat": "Loading chat...", "chatbot.status.chartNotRecommended": "Chart not recommended for this type of data.", "chatbot.status.dataRetrieved": "Data retrieved:", "chatbot.status.executingSql": "Executing SQL query...", "chatbot.status.generatingChart": "Generating chart...", "chatbot.status.generatingSummary": "Generating summary...", "chatbot.status.noResults": "No results found for this query.", "chatbot.status.sqlCorrection": "⚠️ Original SQL failed - Attempting automatic correction...", "chatbot.status.sqlCorrectionSuccess": "✅ Original SQL failed - Automatic correction was successful and executed.", "chatbot.welcome": "Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data,... or any other database information.", "chatHistorySidebar.clearAllHistory": "Clear All History", "chatHistorySidebar.clearSearch": "Clear search", "chatHistorySidebar.delete": "Delete", "chatHistorySidebar.duplicate": "Duplicate", "chatHistorySidebar.hoursAgo_one": "{{count}}h ago", "chatHistorySidebar.hoursAgo_other": "{{count}}h ago", "chatHistorySidebar.justNow": "Just now", "chatHistorySidebar.newChat": "New Chat", "chatHistorySidebar.newConversation": "New conversation", "chatHistorySidebar.noChatSessions": "No chat sessions yet", "chatHistorySidebar.noConversationsFound": "No conversations found", "chatHistorySidebar.rename": "<PERSON><PERSON>", "chatHistorySidebar.searchAria": "Search conversations", "chatHistorySidebar.searchPlaceholder": "Search conversations...", "chatHistorySidebar.startNewConversation": "Start a new conversation", "chatHistorySidebar.title": "Chat History", "chatHistorySidebar.tryDifferentSearch": "Try a different search term", "chatHistorySidebar.yesterday": "Yesterday", "chatMessage.assistantLabel": "Assistant:", "chatMessage.assistantLabelShort": "Assistant:", "chatMessage.assistantThinking": "Assistant is thinking...", "chatMessage.correctedSql": "Corrected SQL {{status}}:", "chatMessage.extractedSql": "Extracted SQL:", "chatMessage.failedSqlQuery": "Failed SQL Query:", "chatMessage.finalSqlQuery": "Final SQL Query:", "chatMessage.hideFullLlmOutput": "Hide Full LLM Output", "chatMessage.initialSqlFailed": "Initial SQL (Failed):", "chatMessage.initialSqlFailedBoth": "The initial SQL failed and the automatic correction attempt also failed.", "chatMessage.initialSqlFailedCorrected": "The initial SQL failed but was automatically corrected and executed successfully.", "chatMessage.intermediateQueryNumber": "Intermediate Query #{{number}}", "chatMessage.processingThoughts": "Processing thoughts", "chatMessage.processingThoughtsGenerating": "Processing thoughts and generating next step...", "chatMessage.resultsNumberRows_one": "Results #{{number}} ({{count}} rows found)", "chatMessage.resultsNumberRows_other": "Results #{{number}} ({{count}} rows found)", "chatMessage.scrollToViewMore": "💡 Scroll to view more data", "chatMessage.showFullLlmOutput": "Show Full LLM Output", "chatMessage.showingAllRows_one": "Showing all {{count}} rows", "chatMessage.showingAllRows_other": "Showing all {{count}} rows", "chatMessage.smartQueryProcessing": "Smart Query Processing", "chatMessage.sqlAutoCorrectionApplied": "SQL Auto-Correction Applied", "chatMessage.sqlExecutionFailed": "SQL Execution Failed", "chatMessage.sqlExecutionFailedBoth": "SQL Execution Failed - Both Attempts", "chatMessage.sqlExecutionFailedMessage": "The generated SQL query could not be executed. Please try rephrasing your question or check if the requested data exists.", "chatMessage.tableDash": "—", "chatMessage.userLabel": "You: ", "common.na": "N/A", "common.notAvailable": "N/A", "consultationSearch.errors.doctorIdNotAvailable": "Doctor ID not available in consultation data", "consultationSearch.errors.enterSearchTerm": "Please enter a search term", "consultationSearch.errors.selectDates": "Please select both start and end dates", "consultationSearch.form.fromDate": "From Date", "consultationSearch.form.searchButton": "Search Consultations", "consultationSearch.form.searchBy": "Search By", "consultationSearch.form.searching": "Searching...", "consultationSearch.form.searchTerm": "Search Term", "consultationSearch.form.searchTermPlaceholder": "", "consultationSearch.form.toDate": "To Date", "consultationSearch.results.count_one": "({{count}} consultation found)", "consultationSearch.results.count_other": "({{count}} consultations found)", "consultationSearch.results.noResults": "No consultations found", "consultationSearch.results.noResultsHint": "Try adjusting your search criteria or search terms", "consultationSearch.results.title": "Search Results", "consultationSearch.subtitle": "Search for consultations by patient, doctor, date, or specialty", "consultationSearch.table.consultationId": "Consultation ID", "consultationSearch.table.dateTime": "Date & Time", "consultationSearch.table.diagnosis": "Diagnosis", "consultationSearch.table.doctor": "Doctor", "consultationSearch.table.patient": "Patient", "consultationSearch.table.specialty": "Specialty", "consultationSearch.title": "Consultation Search", "dashboard.buttons.refresh": "Refresh", "dashboard.errors.consultationTrends": "", "dashboard.errors.departmentStats": "", "dashboard.errors.patientDemographics": "", "dashboard.errors.recentActivity": "", "dashboard.stats.recentConsultations": "Recent Consultations (30 days)", "dashboard.stats.totalConsultations": "Total Consultations", "dashboard.stats.totalDoctors": "Total Doctors", "dashboard.stats.totalPatients": "Total Patients", "dashboard.tableHeaders.avgAge": "Avg Age", "dashboard.tableHeaders.consultations": "Consultations", "dashboard.tableHeaders.department": "Department", "dashboard.tableHeaders.doctors": "Doctors", "dashboard.title": "System Dashboard", "dashboard.widgets.ageDistribution": "Age Distribution", "dashboard.widgets.consultationsBySpecialty": "Consultations by Specialty", "dashboard.widgets.consultationsCount_one": "{{count}} consultations", "dashboard.widgets.consultationsCount_other": "{{count}} consultations", "dashboard.widgets.departmentDetails": "Department Details", "dashboard.widgets.departmentStatistics": "Department Statistics", "dashboard.widgets.genderDistribution": "Gender Distribution", "dashboard.widgets.monthlyConsultationTrends": "Monthly Consultation Trends", "dashboard.widgets.monthlyData": "Monthly Data", "dashboard.widgets.noRecentActivity": "No recent activity found", "dashboard.widgets.patientDemographics": "Patient Demographics", "dashboard.widgets.recentActivity": "Recent Activity", "dashboard.widgets.showingRecentActivities_one": "Showing {{count}} recent activities", "dashboard.widgets.showingRecentActivities_other": "Showing {{count}} recent activities", "dashboard.widgets.specialtyBreakdown": "Specialty Breakdown", "datatable.emptyCell": "-", "datatable.noData": "No data to display", "datatable.scrollHint": "💡 Scroll to view more data", "datatable.showingRows_one": "Showing {{count}} row", "datatable.showingRows_other": "Showing {{count}} rows", "doctorSearch.errors.emptyQuery": "Please enter a search term", "doctorSearch.errors.searchFailed": "Search failed. Please try again.", "doctorSearch.form.department": "Department", "doctorSearch.form.doctorId": "Doctor ID", "doctorSearch.form.license": "License Number", "doctorSearch.form.name": "Doctor Name", "doctorSearch.form.placeholder.department": "Enter department...", "doctorSearch.form.placeholder.doctorId": "Enter doctor ID...", "doctorSearch.form.placeholder.license": "Enter license number...", "doctorSearch.form.placeholder.name": "Enter doctor name...", "doctorSearch.form.placeholder.specialty": "Enter specialty...", "doctorSearch.form.searchButton": "Search Doctors", "doctorSearch.form.searchBy": "Search By", "doctorSearch.form.searching": "Searching...", "doctorSearch.form.searchingDoctors": "Searching doctors...", "doctorSearch.form.searchTerm": "Search Term", "doctorSearch.form.specialty": "Specialty", "doctorSearch.results.count_one": "({{count}} doctor found)", "doctorSearch.results.count_other": "({{count}} doctors found)", "doctorSearch.results.noDoctors": "No doctors found", "doctorSearch.results.title": "Search Results", "doctorSearch.results.tryAdjusting": "Try adjusting your search criteria or search terms", "doctorSearch.subtitle": "Search for doctors by name, specialty, department, or license number", "doctorSearch.table.contact": "Contact", "doctorSearch.table.department": "Department", "doctorSearch.table.doctorId": "Doctor ID", "doctorSearch.table.dr": "Dr.", "doctorSearch.table.licenseNumber": "License Number", "doctorSearch.table.na": "N/A", "doctorSearch.table.name": "Name", "doctorSearch.table.specialty": "Specialty", "doctorSearch.table.status": "Status", "doctorSearch.table.unknown": "Unknown", "doctorSearch.title": "Doctor Search", "errors.authCheckFailed": "Auth check failed:", "errors.fetchPatientDetails": "Failed to fetch patient details", "errors.loadSettings": "Error loading settings:", "errors.loginError": "Login error:", "errors.loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "errors.loginGeneric": "An error occurred during login. Please try again.", "errors.logoutError": "<PERSON><PERSON><PERSON> error:", "errors.network": "Network error. Please check your connection and try again.", "errors.saveSettings": "Error saving settings:", "errors.tryAgain": "Try again", "footer.copyright": "2077 Hospital Assistant System", "footer.help": "Help", "footer.privacyPolicy": "Privacy Policy", "header.defaultUser": "<PERSON><PERSON>", "header.language.en": "EN", "header.language.fr": "FR", "header.logout": "Logout", "header.title": "HOSPITAL ASSISTANT SYSTEM", "header.toggleTheme": "Toggle Theme", "loadingSpinner.text": "text", "login.demoAccounts.admin": "Admin", "login.demoAccounts.doctor": "Doctor", "login.demoAccounts.nurse": "Nurse", "login.demoAccounts.title": "Demo Accounts", "login.footer.copyright": "Hospital Assistant System", "login.form.error": "error", "login.form.loggingIn": "Logging in...", "login.form.login": "<PERSON><PERSON>", "login.form.passwordLabel": "Password", "login.form.passwordPlaceholder": "Enter password", "login.form.title": "Login to Hospital Assistant", "login.form.usernameLabel": "Username", "login.form.usernamePlaceholder": "Enter username", "login.header.title": "HOSPITAL ASSISTANT SYSTEM", "login.header.toggleTheme": "Toggle Theme", "modals.consultationDetails.empty.biometrics": "No vital signs recorded around this consultation.", "modals.consultationDetails.empty.observations": "No clinical observations recorded.", "modals.consultationDetails.empty.prescriptions": "No prescriptions given during this consultation.", "modals.consultationDetails.empty.procedures": "No procedures performed during this consultation.", "modals.consultationDetails.empty.symptoms": "No symptoms recorded for this consultation.", "modals.consultationDetails.fields.consultationId": "Consultation ID", "modals.consultationDetails.fields.dateTime": "Date & Time", "modals.consultationDetails.fields.diagnosis": "Diagnosis", "modals.consultationDetails.fields.doctor": "Doctor", "modals.consultationDetails.fields.dosage": "Dosage:", "modals.consultationDetails.fields.form": "Form:", "modals.consultationDetails.fields.patient": "Patient", "modals.consultationDetails.fields.procedure": "Procedure", "modals.consultationDetails.fields.type": "Type:", "modals.consultationDetails.sections.clinicalObservations": "Clinical Observations", "modals.consultationDetails.sections.consultationInfo": "Consultation Information", "modals.consultationDetails.sections.patientDoctorInfo": "Patient & Doctor Information", "modals.consultationDetails.sections.vitalSignsAroundConsultation": "Vital Signs (Around Consultation)", "modals.consultationDetails.tableHeaders.bloodPressure": "Blood Pressure", "modals.consultationDetails.tableHeaders.date": "Date", "modals.consultationDetails.tableHeaders.observation": "Observation", "modals.consultationDetails.tableHeaders.pulse": "Pulse", "modals.consultationDetails.tableHeaders.respiratoryRate": "Resp. Rate", "modals.consultationDetails.tableHeaders.spo2": "SpO2", "modals.consultationDetails.tableHeaders.temperature": "Temperature", "modals.consultationDetails.tabs.biometrics": "Vital Signs (Around Consultation)", "modals.consultationDetails.tabs.observations": "Clinical Observations", "modals.consultationDetails.tabs.overview": "Overview", "modals.consultationDetails.tabs.prescriptions": "Prescriptions", "modals.consultationDetails.tabs.procedures": "Procedures", "modals.consultationDetails.tabs.symptoms": "Symptoms", "modals.consultationDetails.title": "Consultation Details", "modals.consultationSearch.consultationId": "Consultation ID", "modals.consultationSearch.consultationRowTooltip": "Click to view consultation details", "modals.consultationSearch.dateRange": "Date Range", "modals.consultationSearch.doctorButtonTooltip": "Click to view doctor details", "modals.consultationSearch.doctorName": "Doctor Name", "modals.consultationSearch.fromDate": "From Date", "modals.consultationSearch.loading": "Searching consultations...", "modals.consultationSearch.na": "N/A", "modals.consultationSearch.noResults": "No consultations found", "modals.consultationSearch.noResultsHint": "Try adjusting your search criteria or search terms", "modals.consultationSearch.patientButtonTooltip": "Click to view patient details", "modals.consultationSearch.patientName": "Patient Name", "modals.consultationSearch.resultsCount_one": "({{count}} consultation found)", "modals.consultationSearch.resultsCount_other": "({{count}} consultations found)", "modals.consultationSearch.resultsTitle": "Search Results", "modals.consultationSearch.searchButton": "Search Consultations", "modals.consultationSearch.searchBy": "Search By", "modals.consultationSearch.searching": "Searching...", "modals.consultationSearch.searchPlaceholder": "", "modals.consultationSearch.searchTerm": "Search Term", "modals.consultationSearch.searchType": "Search Type", "modals.consultationSearch.specialty": "Specialty", "modals.consultationSearch.table.dateTime": "Date & Time", "modals.consultationSearch.table.diagnosis": "Diagnosis", "modals.consultationSearch.table.doctor": "Doctor", "modals.consultationSearch.table.id": "ID", "modals.consultationSearch.table.patient": "Patient", "modals.consultationSearch.table.specialty": "Specialty", "modals.consultationSearch.title": "Consultation Search", "modals.consultationSearch.toDate": "To Date", "modals.deleteChatSession.cancel": "Cancel", "modals.deleteChatSession.delete": "Delete", "modals.deleteChatSession.title": "Delete Chat Session?", "modals.deleteChatSession.warning": "This action cannot be undone. The chat session and all its messages will be permanently deleted.", "modals.doctorDetails.buttons.viewConsultation": "View Consultation", "modals.doctorDetails.buttons.viewPatient": "View Patient", "modals.doctorDetails.consultationId": "Consultation #{{consultationId}}", "modals.doctorDetails.fields.doctorId": "Doctor ID", "modals.doctorDetails.fields.email": "Email", "modals.doctorDetails.fields.fullName": "Full Name", "modals.doctorDetails.fields.specialty": "Specialty", "modals.doctorDetails.labels.diagnosis": "Diagnosis:", "modals.doctorDetails.labels.patient": "Patient:", "modals.doctorDetails.noConsultations": "No consultations found.", "modals.doctorDetails.noPatients": "No patients found.", "modals.doctorDetails.noStatistics": "No statistics available.", "modals.doctorDetails.sections.activitySummary": "Activity Summary", "modals.doctorDetails.sections.doctorInfo": "Doctor Information", "modals.doctorDetails.sections.patientsTreated": "Patients Treated", "modals.doctorDetails.sections.practiceOverview": "Practice Overview", "modals.doctorDetails.sections.practicePeriod": "Practice Period", "modals.doctorDetails.sections.practiceStatistics": "Practice Statistics", "modals.doctorDetails.sections.recentConsultations": "Recent Consultations", "modals.doctorDetails.stats.activeDays": "Active Days:", "modals.doctorDetails.stats.activeDaysColon": "Active Days:", "modals.doctorDetails.stats.firstConsultation": "First Consultation:", "modals.doctorDetails.stats.lastConsultation": "Last Consultation:", "modals.doctorDetails.stats.patientsTreated": "Patients Treated", "modals.doctorDetails.stats.totalConsultations": "Total Consultations:", "modals.doctorDetails.stats.totalConsultationsColon": "Total Consultations:", "modals.doctorDetails.stats.uniquePatients": "Unique Patients:", "modals.doctorDetails.tableHeaders.consultations": "Consultations", "modals.doctorDetails.tableHeaders.lastVisit": "Last Visit", "modals.doctorDetails.tableHeaders.patientName": "Patient Name", "modals.doctorDetails.tabs.consultations": "Consultations", "modals.doctorDetails.tabs.overview": "Overview", "modals.doctorDetails.tabs.patients": "Patients", "modals.doctorDetails.tabs.statistics": "Statistics", "modals.doctorDetails.title": "Doctor <PERSON>", "modals.doctorSearch.button.search": "Search Doctors", "modals.doctorSearch.button.searching": "Searching...", "modals.doctorSearch.error.emptyQuery": "Please enter a search term", "modals.doctorSearch.error.searchFailed": "Search failed. Please try again.", "modals.doctorSearch.loading": "Searching doctors...", "modals.doctorSearch.placeholder.name": "Enter doctor name...", "modals.doctorSearch.results.count_one": "({{count}} doctor found)", "modals.doctorSearch.results.count_other": "({{count}} doctors found)", "modals.doctorSearch.results.noDoctors": "No doctors found", "modals.doctorSearch.results.title": "Search Results", "modals.doctorSearch.results.tryAdjusting": "Try adjusting your search criteria or search terms", "modals.doctorSearch.searchBy": "Search By", "modals.doctorSearch.searchTerm": "Search Term", "modals.doctorSearch.searchType": "Search Type", "modals.doctorSearch.tableHeaders.contact": "Contact", "modals.doctorSearch.tableHeaders.department": "Department", "modals.doctorSearch.tableHeaders.doctorId": "Doctor ID", "modals.doctorSearch.tableHeaders.name": "Name", "modals.doctorSearch.tableHeaders.specialty": "Specialty", "modals.doctorSearch.tableHeaders.status": "Status", "modals.doctorSearch.tableRow.dr": "Dr.", "modals.doctorSearch.tableRow.na": "N/A", "modals.doctorSearch.tableRow.viewDoctorDetails": "Click to view doctor details", "modals.doctorSearch.title": "Doctor Search", "modals.doctorSearch.type.department": "Department", "modals.doctorSearch.type.doctorId": "Doctor ID", "modals.doctorSearch.type.email": "Email", "modals.doctorSearch.type.name": "Doctor Name", "modals.doctorSearch.type.specialty": "Specialty", "modals.patientDetails.address": "Address", "modals.patientDetails.bloodPressure": "Blood Pressure", "modals.patientDetails.date": "Date", "modals.patientDetails.dateOfBirth": "Date of Birth", "modals.patientDetails.doctor": "Doctor", "modals.patientDetails.dosage": "Dosage", "modals.patientDetails.form": "Form", "modals.patientDetails.fullName": "Full Name", "modals.patientDetails.gender": "Gender", "modals.patientDetails.genderFemale": "Female", "modals.patientDetails.genderMale": "Male", "modals.patientDetails.medicalHistory": "Medical History", "modals.patientDetails.noConsultations": "No consultations found.", "modals.patientDetails.noDiagnosis": "No diagnosis", "modals.patientDetails.noMedicalHistory": "No medical history recorded.", "modals.patientDetails.noPrescriptions": "No prescriptions found.", "modals.patientDetails.noVitalSigns": "No vital signs recorded.", "modals.patientDetails.patientId": "Patient ID", "modals.patientDetails.personalInfo": "Personal Information", "modals.patientDetails.phone": "Phone", "modals.patientDetails.prescribedFor": "Prescribed for", "modals.patientDetails.pulse": "Pulse", "modals.patientDetails.recentConsultations": "Recent Consultations", "modals.patientDetails.recentMedications": "Recent Medications", "modals.patientDetails.recentVitalSigns": "Recent Vital Signs", "modals.patientDetails.respiratoryRate": "Resp. Rate", "modals.patientDetails.specialty": "Specialty", "modals.patientDetails.spo2": "SpO2", "modals.patientDetails.tabs.biometrics": "Vital Signs", "modals.patientDetails.tabs.consultations": "Consultations", "modals.patientDetails.tabs.history": "Medical History", "modals.patientDetails.tabs.overview": "Overview", "modals.patientDetails.tabs.prescriptions": "Medications", "modals.patientDetails.temperature": "Temperature", "modals.patientDetails.title": "Patient Details", "modals.patientDetails.unknownDoctor": "Unknown", "modals.patientSearch.buttons.search": "Search Patients", "modals.patientSearch.buttons.searching": "Searching...", "modals.patientSearch.error.emptyQuery": "Please enter a search term", "modals.patientSearch.error.icon": "Error", "modals.patientSearch.error.searchFailed": "Search failed. Please try again.", "modals.patientSearch.loading": "Searching patients...", "modals.patientSearch.placeholder.id": "Enter patient ID...", "modals.patientSearch.placeholder.name": "Enter patient name...", "modals.patientSearch.placeholder.phone": "Enter phone number...", "modals.patientSearch.results.count_one": "({{count}} patient found)", "modals.patientSearch.results.count_other": "({{count}} patients found)", "modals.patientSearch.results.noPatients": "No patients found", "modals.patientSearch.results.noPatientsHelp": "Try adjusting your search criteria or search terms", "modals.patientSearch.results.noPatientsIcon": "No patients found", "modals.patientSearch.results.title": "Search Results", "modals.patientSearch.searchBy": "Search By", "modals.patientSearch.searchTerm": "Search Term", "modals.patientSearch.searchType.id": "Patient ID", "modals.patientSearch.searchType.name": "Patient Name", "modals.patientSearch.searchType.phone": "Phone Number", "modals.patientSearch.table.birthDate": "Birth Date", "modals.patientSearch.table.contact": "Contact", "modals.patientSearch.table.gender": "Gender", "modals.patientSearch.table.genderFemale": "Female", "modals.patientSearch.table.genderMale": "Male", "modals.patientSearch.table.na": "N/A", "modals.patientSearch.table.name": "Name", "modals.patientSearch.table.patientId": "Patient ID", "modals.patientSearch.table.phoneIcon": "Phone", "modals.patientSearch.table.rowTooltip": "Click to view patient details", "modals.patientSearch.title": "Patient Search", "modals.searchModal.badge": "Search", "modals.searchModal.tipsText": "Use the dropdown to select your search criteria, then enter your search term. Results will appear below.", "modals.searchModal.tipsTitle": "Search Tips", "modals.searchModal.title": "title", "patientSearch.buttons.clear": "Clear", "patientSearch.buttons.search": "Search", "patientSearch.buttons.searching": "Searching...", "patientSearch.errors.emptySearch": "Please enter a search term", "patientSearch.errors.searchFailed": "Search failed. Please try again.", "patientSearch.gender.female": "Female", "patientSearch.gender.male": "Male", "patientSearch.gender.unknown": "N/A", "patientSearch.inputPlaceholder": "Enter {{type}}...", "patientSearch.loading": "Searching patients...", "patientSearch.noResults": "No patients found", "patientSearch.noResultsHint": "Try adjusting your search criteria or search term.", "patientSearch.results.count_one": "({{count}} patient found)", "patientSearch.results.count_other": "({{count}} patients found)", "patientSearch.results.title": "Search Results", "patientSearch.searchBy": "Search By:", "patientSearch.searchType.diagnosis": "Diagnosis", "patientSearch.searchType.email": "Email", "patientSearch.searchType.id": "Patient ID", "patientSearch.searchType.name": "Patient Name", "patientSearch.searchType.phone": "Phone Number", "patientSearch.tableHeaders.birthDate": "Birth Date", "patientSearch.tableHeaders.contact": "Contact", "patientSearch.tableHeaders.fullName": "Full Name", "patientSearch.tableHeaders.gender": "Gender", "patientSearch.tableHeaders.patientId": "Patient ID", "patientSearch.title": "Patient Search", "settings.about": "About", "settings.aboutDesc": "Hospital Assistant System - AI-powered hospital management platform", "settings.appearance": "Appearance", "settings.build": "Build:", "settings.changePassword": "Change Password", "settings.confirmNewPassword": "Confirm New Password", "settings.currentPassword": "Current Password", "settings.currentTheme": "Current: {{mode}} mode", "settings.dateFormat": "Date Format", "settings.dateFormatEu": "DD/MM/YYYY", "settings.dateFormatIso": "YYYY-MM-DD", "settings.dateFormatUs": "MM/DD/YYYY", "settings.displayName": "Display Name", "settings.emailNotifications": "Email Notifications", "settings.emailNotificationsDesc": "Receive notifications via email", "settings.english": "English", "settings.french": "Français", "settings.language": "Language", "settings.lastUpdated": "Last Updated:", "settings.lastUpdatedDate": "January 1, 2077", "settings.newPassword": "New Password", "settings.notifications": "Notifications", "settings.pushNotifications": "Push Notifications", "settings.pushNotificationsDesc": "Receive browser push notifications", "settings.pwChangeButton": "Change Password", "settings.pwChanging": "Changing...", "settings.pwFailed": "Failed to change password.", "settings.pwNetwork": "Network error. Please try again.", "settings.pwNoMatch": "New passwords do not match.", "settings.pwRequired": "All fields are required.", "settings.pwSuccess": "Password changed successfully.", "settings.role": "Role", "settings.saveButton": "Save Settings", "settings.saveFailed": "Failed to save settings. Please try again.", "settings.saveSuccess": "Setting<PERSON> saved successfully!", "settings.saving": "Saving...", "settings.switchTheme": "Switch theme", "settings.switchTo": "Switch to {{mode}}", "settings.theme": "Theme", "settings.title": "Settings", "settings.toggleOff": "Turn on", "settings.toggleOn": "Turn off", "settings.username": "Username", "settings.userProfile": "User Profile", "settings.version": "Version:", "sidebar.chatbot": "<PERSON><PERSON><PERSON>", "sidebar.closeMenu": "Close navigation menu", "sidebar.dashboard": "Dashboard", "sidebar.navigationMenu": "Navigation Menu", "sidebar.openMenu": "Open navigation menu", "sidebar.settings": "Settings", "user.defaultName": "User", "user.defaultRole": "User", "widget.errorLoadingData": "Error Loading Data", "widget.loading": "Loading {{title}}..."}