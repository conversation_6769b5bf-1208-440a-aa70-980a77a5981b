#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update demo accounts in the hospital database and sync to app database.
This script will update the Medecins table with the new demo data and then sync users.
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from passlib.context import CryptContext
from datetime import datetime

# Add the backend directory to the path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from config import (
        HOSPITAL_DB_HOST, HOSPITAL_DB_PORT, HOSPITAL_DB_NAME, 
        HOSPITAL_DB_USER, HOSPITAL_DB_PASSWORD,
        APP_DB_HOST, APP_DB_PORT, APP_DB_NAME, 
        APP_DB_USER, APP_DB_PASSWORD
    )
except ImportError:
    # Fallback to environment variables
    HOSPITAL_DB_HOST = os.environ.get('HOSPITAL_DB_HOST', 'localhost')
    HOSPITAL_DB_PORT = int(os.environ.get('HOSPITAL_DB_PORT', 5433))
    HOSPITAL_DB_NAME = os.environ.get('HOSPITAL_DB_NAME', 'postgres')
    HOSPITAL_DB_USER = os.environ.get('HOSPITAL_DB_USER', 'postgres')
    HOSPITAL_DB_PASSWORD = os.environ.get('HOSPITAL_DB_PASSWORD', '')
    
    APP_DB_HOST = os.environ.get('APP_DB_HOST', 'localhost')
    APP_DB_PORT = int(os.environ.get('APP_DB_PORT', 5434))
    APP_DB_NAME = os.environ.get('APP_DB_NAME', 'postgres')
    APP_DB_USER = os.environ.get('APP_DB_USER', 'postgres')
    APP_DB_PASSWORD = os.environ.get('APP_DB_PASSWORD', '')

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Demo accounts data based on the provided image
DEMO_ACCOUNTS = [
    {
        'username': 'admin',
        'first_name': 'Admin',
        'last_name': 'User',
        'specialite': 'Administration'
    },
    {
        'username': 'bertrandd1',
        'first_name': 'David',
        'last_name': 'Bertrand',
        'specialite': 'Cardiology'
    },
    {
        'username': 'legally1',
        'first_name': 'Yann',
        'last_name': 'Le Gall',
        'specialite': 'Neurology'
    },
    {
        'username': 'lopeze1',
        'first_name': 'Elena',
        'last_name': 'Lopez',
        'specialite': 'Pediatrics'
    },
    {
        'username': 'petitc1',
        'first_name': 'Camille',
        'last_name': 'Petit',
        'specialite': 'Dermatology'
    }
]

def get_hospital_connection():
    """Get connection to hospital database"""
    return psycopg2.connect(
        host=HOSPITAL_DB_HOST,
        port=HOSPITAL_DB_PORT,
        database=HOSPITAL_DB_NAME,
        user=HOSPITAL_DB_USER,
        password=HOSPITAL_DB_PASSWORD,
        cursor_factory=RealDictCursor
    )

def get_app_connection():
    """Get connection to app database"""
    return psycopg2.connect(
        host=APP_DB_HOST,
        port=APP_DB_PORT,
        database=APP_DB_NAME,
        user=APP_DB_USER,
        password=APP_DB_PASSWORD,
        cursor_factory=RealDictCursor
    )

def update_hospital_demo_accounts():
    """Update the Medecins table with demo accounts"""
    print("Updating hospital database with demo accounts...")
    
    with get_hospital_connection() as conn:
        with conn.cursor() as cursor:
            # Clear existing demo accounts (except admin which is handled separately)
            cursor.execute("DELETE FROM Medecins")
            
            # Insert new demo accounts (skip admin as it's not a doctor)
            for account in DEMO_ACCOUNTS[1:]:  # Skip admin account
                cursor.execute("""
                    INSERT INTO Medecins (Nom, Prenom, Specialite, Email)
                    VALUES (%s, %s, %s, %s)
                """, (
                    account['last_name'],
                    account['first_name'],
                    account['specialite'],
                    f"{account['username']}@hospital.com"
                ))
            
            conn.commit()
            print(f"✅ Successfully updated {len(DEMO_ACCOUNTS)-1} doctor accounts in hospital database")

def update_app_demo_accounts():
    """Update the app database with demo accounts"""
    print("Updating app database with demo accounts...")
    
    with get_app_connection() as conn:
        with conn.cursor() as cursor:
            # Get role IDs
            cursor.execute("SELECT id, name FROM role")
            roles = {row['name']: row['id'] for row in cursor.fetchall()}
            
            # Get department IDs
            cursor.execute("SELECT id, name FROM department")
            departments = {row['name']: row['id'] for row in cursor.fetchall()}
            
            # Clear existing users (except admin if it exists)
            cursor.execute("DELETE FROM \"user\" WHERE username != 'admin'")
            
            # Update or create admin user
            admin_account = DEMO_ACCOUNTS[0]
            admin_role_id = roles.get('admin')
            admin_dept_id = departments.get('Administration') or departments.get('Emergency') or list(departments.values())[0]
            
            password_hash = pwd_context.hash('admin123')
            cursor.execute("""
                INSERT INTO "user" (
                    username, first_name, last_name, password_hash,
                    department_id, role_id, is_active, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (username) DO UPDATE SET
                    first_name = EXCLUDED.first_name,
                    last_name = EXCLUDED.last_name,
                    password_hash = EXCLUDED.password_hash,
                    updated_at = EXCLUDED.updated_at
            """, (
                admin_account['username'],
                admin_account['first_name'],
                admin_account['last_name'],
                password_hash,
                admin_dept_id,
                admin_role_id,
                True,
                datetime.now(),
                datetime.now()
            ))
            
            # Create doctor users
            doctor_role_id = roles.get('doctor') or roles.get('user')
            
            for account in DEMO_ACCOUNTS[1:]:  # Skip admin account
                # Map specialties to departments
                dept_id = departments.get(account['specialite'])
                if not dept_id:
                    # Use first available department if specialty not found
                    dept_id = list(departments.values())[0]
                
                password_hash = pwd_context.hash('doctor123')
                cursor.execute("""
                    INSERT INTO "user" (
                        username, first_name, last_name, password_hash,
                        department_id, role_id, is_active, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (username) DO UPDATE SET
                        first_name = EXCLUDED.first_name,
                        last_name = EXCLUDED.last_name,
                        password_hash = EXCLUDED.password_hash,
                        updated_at = EXCLUDED.updated_at
                """, (
                    account['username'],
                    account['first_name'],
                    account['last_name'],
                    password_hash,
                    dept_id,
                    doctor_role_id,
                    True,
                    datetime.now(),
                    datetime.now()
                ))
            
            conn.commit()
            print(f"✅ Successfully updated {len(DEMO_ACCOUNTS)} user accounts in app database")

def verify_accounts():
    """Verify that the accounts were created correctly"""
    print("\nVerifying demo accounts...")
    
    with get_app_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT u.id, u.username, u.first_name, u.last_name, 
                       r.name as role_name, d.name as department_name
                FROM "user" u
                LEFT JOIN role r ON u.role_id = r.id
                LEFT JOIN department d ON u.department_id = d.id
                ORDER BY u.id
            """)
            
            users = cursor.fetchall()
            
            print("\n📋 Current user accounts:")
            print("ID | Username     | First Name | Last Name | Role     | Department")
            print("-" * 70)
            
            for user in users:
                print(f"{user['id']:2} | {user['username']:12} | {user['first_name']:10} | {user['last_name']:9} | {user['role_name']:8} | {user['department_name'] or 'N/A'}")

def main():
    """Main function to update demo accounts"""
    print("🏥 Hospital Demo Account Updater")
    print("=" * 50)
    
    try:
        # Update hospital database
        update_hospital_demo_accounts()
        
        # Update app database
        update_app_demo_accounts()
        
        # Verify accounts
        verify_accounts()
        
        print("\n✅ Demo accounts updated successfully!")
        print("\nLogin credentials:")
        print("- Admin: username='admin', password='admin123'")
        print("- Doctors: username='bertrandd1', 'legally1', 'lopeze1', 'petitc1', password='doctor123'")
        
    except Exception as e:
        print(f"❌ Error updating demo accounts: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
