import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const Login = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { login, isAuthenticated } = useAuth();
  const { theme, toggleTheme } = useTheme();

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/chatbot" replace />;
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    const result = await login(formData.username, formData.password);
    
    if (!result.success) {
      setError(result.error);
    }
    
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 px-6 py-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <span className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          {t('login.header.title', { defaultValue: 'HOSPITAL ASSISTANT SYSTEM' })}
        </span>
        <button
          onClick={toggleTheme}
          className="flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          aria-label={t('login.header.toggleTheme', { defaultValue: 'Toggle Theme' })}
        >
          <i className={`fas ${theme === 'dark' ? 'fa-sun' : 'fa-moon'} mr-2`}></i>
          {t('login.header.toggleTheme', { defaultValue: 'Toggle Theme' })}
        </button>
      </div>

      {/* Login Form */}
      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 transition-colors duration-300">
          {/* Logo */}
          <div className="text-center mb-8">
            <i className="fas fa-hospital-user text-4xl text-blue-600 mb-4"></i>
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
              {t('login.form.title', { defaultValue: 'Login to Hospital Assistant' })}
            </h2>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-3 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 rounded-md text-sm">
              {t('login.form.error', { defaultValue: error })}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <i className="fas fa-user mr-2 text-gray-500"></i>
                {t('login.form.usernameLabel', { defaultValue: 'Username' })}
              </label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder={t('login.form.usernamePlaceholder', { defaultValue: 'Enter username' })}
                required
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <i className="fas fa-lock mr-2 text-gray-500"></i>
                {t('login.form.passwordLabel', { defaultValue: 'Password' })}
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder={t('login.form.passwordPlaceholder', { defaultValue: 'Enter password' })}
                required
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
              aria-label={isLoading ? t('login.form.loggingIn', { defaultValue: 'Logging in...' }) : t('login.form.login', { defaultValue: 'Login' })}
            >
              {isLoading ? (
                <i className="fas fa-spinner animate-spin mr-2"></i>
              ) : (
                <i className="fas fa-sign-in-alt mr-2"></i>
              )}
              {isLoading
                ? t('login.form.loggingIn', { defaultValue: 'Logging in...' })
                : t('login.form.login', { defaultValue: 'Login' })}
            </button>
          </form>

          {/* Demo Accounts */}
          <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3">
              {t('login.demoAccounts.title', { defaultValue: 'Demo Accounts' })}
            </h3>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div><strong>{t('login.demoAccounts.admin', { defaultValue: 'Admin' })}:</strong> admin / admin123</div>
              <div><strong>{t('login.demoAccounts.doctor', { defaultValue: 'Doctor' })}:</strong> doctor / doctor123</div>
              <div><strong>{t('login.demoAccounts.nurse', { defaultValue: 'Nurse' })}:</strong> nurse / nurse123</div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white dark:bg-gray-800 px-6 py-4 text-center border-t border-gray-200 dark:border-gray-700 text-sm text-gray-600 dark:text-gray-400">
        &copy; 2077 {t('login.footer.copyright', { defaultValue: 'Hospital Assistant System' })}
      </div>
    </div>
  );
};

export default Login;